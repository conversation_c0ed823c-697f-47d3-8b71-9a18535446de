# Arquitectura del Proyecto: DDD y Arquitectura Hexagonal

Este documento describe la arquitectura del proyecto, que se basa en los principios de **Domain-Driven Design (DDD)** y la **Arquitectura Hexagonal** (también conocida como Puertos y Adaptadores).

## 1. Conceptos Fundamentales

### Arquitectura Hexagonal (Puertos y Adaptadores)

El objetivo de esta arquitectura es aislar el núcleo de la lógica de negocio de las dependencias externas (como bases de datos, interfaces de usuario, APIs de terceros, etc.).

- **El Hexágono (El Interior):** Contiene la lógica de negocio pura, dividida en dos capas:

  1.  **`domain`**: El corazón de la aplicación. Modela el conocimiento del negocio, sus reglas y sus procesos. No depende de ninguna otra capa.
  2.  **`application`**: Orquesta los casos de uso. Actúa como un coordinador que utiliza el modelo de dominio para realizar tareas.

- **Puertos (Ports):** Son interfaces definidas en la capa de `application` que establecen contratos sobre cómo se interactúa con el núcleo de la aplicación (`puertos de entrada`) o cómo el núcleo interactúa con el exterior (`puertos de salida`).

- **Adaptadores (Adapters):** Son la implementación de los puertos y residen en la capa de `infrastructure`.
  - **Adaptadores de Entrada (Driving Adapters):** Invocan los casos de uso. Ejemplos: controladores REST, consumidores de mensajes, clientes gRPC.
  - **Adaptadores de Salida (Driven Adapters):** Son invocados por el núcleo de la aplicación. Ejemplos: repositorios de bases de datos, clientes de APIs externas.

### Domain-Driven Design (DDD)

DDD es un enfoque de desarrollo de software que se centra en modelar un dominio de negocio complejo y vincular esa implementación a los conceptos centrales del negocio. Utilizamos sus bloques de construcción (Value Objects, Entities, Aggregates) para crear un modelo de dominio rico y expresivo.

---

## 2. Estructura de Directorios

La estructura del paquete `.../hexa` refleja directamente estos conceptos arquitectónicos.

```
hexa/
├── domain/
│   ├── model/
│   │   ├── alias/
│   │   ├── conversation/
│   │   ├── filter/
│   │   └── ... (otros subdominios)
│   └── service/
├── application/
│   ├── port/
│   │   ├── in/  (Casos de Uso - Interfaces)
│   │   └── out/ (Abstracciones de Infraestructura - Interfaces)
│   ├── service/ (Implementación de Casos de Uso)
│   ├── dto/
│   └── utils/
└── infrastructure/
    └── adapter/
        ├── in/  (Controladores, Consumidores)
        └── out/ (Repositorios, Clientes API)
```

### `domain`

La capa más interna y el corazón de la aplicación.

- **`domain/model`**: Contiene los bloques de construcción de DDD:
  - **Value Objects**: Objetos inmutables definidos por sus atributos (ej: `EmailAddress`, `DateRangeFilter`).
  - **Entities**: Objetos con identidad que pueden cambiar a lo largo del tiempo.
  - **Aggregates**: Clústeres de objetos de dominio que se tratan como una única unidad.
- **`domain/service`**: Contiene lógica de negocio que no encaja de forma natural en ningún objeto del modelo.

### `application`

Orquesta la ejecución de los casos de uso. No contiene lógica de negocio.

- **`application/port/in`**: Define los casos de uso que la aplicación ofrece. Son interfaces (ej: `SearchConversationsUseCase`).
- **`application/port/out`**: Define las dependencias que la aplicación necesita del exterior (ej: `ConversationRepository`).
- **`application/service`**: Implementa las interfaces de los puertos de entrada.
- **`application/dto`**: Data Transfer Objects para transferir datos hacia y desde el exterior sin exponer el modelo de dominio.

### `infrastructure`

Contiene todo lo que interactúa con el mundo exterior.

- **`infrastructure/adapter/in`**: Implementa los puntos de entrada. Un controlador REST que recibe una petición y llama a un `UseCase` en la capa de aplicación es un adaptador de entrada.
- **`infrastructure/adapter/out`**: Implementa los puertos de salida. Una clase que implementa la interfaz `ConversationRepository` para guardar datos en MongoDB es un adaptador de salida.

---

## 3. Flujo de una Petición

Un flujo típico sigue esta secuencia:

1.  Un **Adaptador de Entrada** (ej: `ConversationController`) recibe una petición.
2.  El adaptador valida y mapea la entrada a un DTO o a tipos primitivos.
3.  Llama a un **Puerto de Entrada** (ej: `ImportConversationsUseCase`) en la capa de `application`.
4.  El **Servicio de Aplicación** (la implementación del caso de uso) orquesta la lógica:
    a. Utiliza el **Modelo de Dominio** para ejecutar las reglas de negocio.
    b. Llama a uno o más **Puertos de Salida** (ej: `ConversationRepository.save(...)`) para persistir los cambios.
5.  El **Adaptador de Salida** (ej: `MongoConversationRepository`) implementa la lógica de persistencia.
6.  El flujo regresa hasta el adaptador de entrada, que genera una respuesta.

---

## 4. Próximos Pasos Sugeridos

La base actual es muy sólida. El desarrollo debería continuar siguiendo este orden para mantener la integridad de la arquitectura:

1.  **Definir Puertos de Entrada (Casos de Uso):** Crear las interfaces en `application/port/in` para las funcionalidades principales.
2.  **Definir Puertos de Salida (Repositorios/Servicios Externos):** Crear las interfaces en `application/port/out` que necesitarán los casos de uso.
3.  **Implementar los Servicios de Aplicación:** Crear las clases en `application/service` que implementen los puertos de entrada, orquestando la lógica y llamando a los puertos de salida.
4.  **Implementar los Adaptadores de Salida:** Crear las implementaciones concretas en `infrastructure/adapter/out` (ej: repositorios para MongoDB/JPA).
5.  **Implementar los Adaptadores de Entrada:** Crear las clases en `infrastructure/adapter/in` (ej: controladores REST) que exponen los casos de uso al exterior.
