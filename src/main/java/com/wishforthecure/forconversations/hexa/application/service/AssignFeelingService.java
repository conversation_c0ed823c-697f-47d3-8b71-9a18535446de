package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.domain.port.AssignFeelingUseCase;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.repository.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AssignFeelingService implements AssignFeelingUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> assignFeeling(ConversationDomainId conversationDomainId, FeelingDomain feeling) {
        return null;
    }
}
