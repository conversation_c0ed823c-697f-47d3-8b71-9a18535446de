package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.tag.port.RemoveTagUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RemoveTagService implements RemoveTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> removeTag(ConversationDomainId conversationId, String tagName) {
        return null;
    }
}
