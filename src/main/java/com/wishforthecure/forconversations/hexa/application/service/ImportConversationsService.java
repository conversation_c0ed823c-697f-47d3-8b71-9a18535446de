package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.domain.port.ImportConversationsUseCase;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.repository.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ImportConversationsService implements ImportConversationsUseCase {

    private final MBoxUtils mboxUtils;
    private final ConversationRepository conversationRepository;

    @Override
    public void importFromMbox(byte[] mboxFileBytes) {

    }

}
