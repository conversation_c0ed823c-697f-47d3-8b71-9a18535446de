package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.source.port.MessagePort;
import com.wishforthecure.forconversations.hexa.application.port.out.MessageRepository;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class MessageService implements MessagePort {

    private final MessageRepository messageRepository;

    @Override
    public Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain) {
        return messageRepository.save(emailMessageDomain);
    }

    @Override
    public Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> emailMessageDomainList) {
        return messageRepository.saveAll(emailMessageDomainList).collectList();
    }
}
