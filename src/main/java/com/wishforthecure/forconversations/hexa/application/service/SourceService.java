package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.source.port.SourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.out.persistence.entity.SourceEntity;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class SourceService implements SourcePort {

    private final SourceRepository sourceRepository;

    @Override
    public Mono<SourceDTO> save(SourceDTO sourceDTO) {
        SourceEntity source = mapToDomain(sourceDTO);
        return null;
    }

    private SourceEntity mapToDomain(SourceDTO dto) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }

    private SourceDTO mapToDTO(SourceEntity source) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }
}
