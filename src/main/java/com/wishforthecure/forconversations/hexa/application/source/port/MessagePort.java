package com.wishforthecure.forconversations.hexa.application.source.port;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;

import java.util.List;
import reactor.core.publisher.Mono;

public interface MessagePort {
    Mono<EmailMessageDomain> save(EmailMessageDomain emailMessageDomain);

    Mono<List<EmailMessageDomain>> saveAll(List<EmailMessageDomain> messageDTOList);
}
