package com.wishforthecure.forconversations.hexa.application.source.adapter;

import com.wishforthecure.forconversations.hexa.application.source.port.WhatsAppSourcePort;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.WhatsAppSourceUploadDTO;
import reactor.core.publisher.Mono;

;

public class WhatsAppSourceAdapter implements WhatsAppSourcePort {
    @Override
    public Mono<WhatsAppSourceSaveDTO> upload(WhatsAppSourceUploadDTO whatsAppSourceUploadDTO) {
        return null;
    }

    @Override
    public Mono<WhatsAppSourceSaveDTO> save(WhatsAppSourceSaveDTO whatsAppSourceSaveDTO) {
        return null;
    }
}
