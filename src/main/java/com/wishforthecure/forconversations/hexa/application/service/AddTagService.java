package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.tag.port.AddTagUseCase;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.repository.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AddTagService implements AddTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> addTag(ConversationDomainId conversationDomainId, String tagName) {
        return null;
    }
}
