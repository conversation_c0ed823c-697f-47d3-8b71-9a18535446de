package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.domain.port.CreateConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.service.SourceParsingService;
import com.wishforthecure.forconversations.repository.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class CreateConversationService implements CreateConversationUseCase {

    private final SourceRepository sourceRepository;
    private final SourceParsingService parsingService;
    private final ConversationRepository conversationRepository;

    @Override
    public Mono<ConversationDomainId> create(CreateConversationCommand command) {
        return null;
    }
}
