package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.domain.port.RenameConversationUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class RenameConversationService implements RenameConversationUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> rename(ConversationDomainId conversationId, String newName) {
        return null;
    }
}
