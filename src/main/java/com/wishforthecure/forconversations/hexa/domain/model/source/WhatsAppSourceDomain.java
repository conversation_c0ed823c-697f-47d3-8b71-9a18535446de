package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jmolecules.ddd.annotation.AggregateRoot;
import org.springframework.data.annotation.TypeAlias;

import java.util.List;

@AggregateRoot
@TypeAlias("whatsAppSource")
@Data
@EqualsAndHashCode(callSuper = true)
public class WhatsAppSourceDomain extends AbstractSourceDomain {

    private List<WhatsAppMessageDomain> messages;

    private SourceDomainType sourceType = SourceDomainType.WHATSAPP;

    @Override
    public List<WhatsAppMessageDomain> getMessages() {
        return messages;
    }
}
