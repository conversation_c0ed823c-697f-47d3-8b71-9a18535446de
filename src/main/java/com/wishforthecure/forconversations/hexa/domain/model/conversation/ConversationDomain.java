package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public class ConversationDomain {

    ConversationDomainId id;
    String name;
    Instant startDate;
    Instant endDate;
    Set<FeelingDomain> feelings;
    Set<TagDomain> tags;
    List<MessageDomain> messages;

    public static ConversationDomain create(ConversationDomainId id, String name, Instant startDate, Instant endDate,
            Set<FeelingDomain> feelings, Set<TagDomain> tags, List<MessageDomain> messages) {
        return new ConversationDomain(id, name, startDate, endDate, feelings, tags, messages);
    }
}
