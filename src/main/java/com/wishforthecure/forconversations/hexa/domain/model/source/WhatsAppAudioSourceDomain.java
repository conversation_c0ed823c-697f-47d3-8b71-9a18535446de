package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppAudioMessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhatsAppAudioSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<WhatsAppAudioMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.WHATSAPP;
}
