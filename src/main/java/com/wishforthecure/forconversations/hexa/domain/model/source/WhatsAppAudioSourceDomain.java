package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppAudioMessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WhatsAppAudioSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<WhatsAppAudioMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.WHATSAPP_AUDIO;

    @Override
    public SourceDomainId getSourceId() {
        return sourceId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<MessageDomain> getMessages() {
        return (List<MessageDomain>) (List<?>) messages;
    }

    public List<WhatsAppAudioMessageDomain> getWhatsAppAudioMessages() {
        return messages;
    }

    @Override
    public byte[] getSource() {
        return source;
    }

    public static WhatsAppAudioSourceDomain create(SourceDomainId sourceId, Instant time,
                                                 List<WhatsAppAudioMessageDomain> messages, byte[] source) {
        return new WhatsAppAudioSourceDomain(sourceId, time, messages, source, SourceDomainType.WHATSAPP_AUDIO);
    }
}
