package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.util.List;
import java.util.stream.Collectors;
import org.jmolecules.ddd.annotation.Service;

@Service
public class MessageFilteringService {

    public List<MessageDomain> filterMessages(List<? extends MessageDomain> messages, List<MessageFilter> filters) {
        if (filters == null || filters.isEmpty()) {
            return List.copyOf(messages);
        }

        return messages.stream().filter(message -> passesAllFilters(message, filters)).collect(Collectors.toList());
    }

    private boolean passesAllFilters(MessageDomain message, List<MessageFilter> filters) {
        for (MessageFilter filter : filters) {
            if (!filter.apply(message)) {
                return false;
            }
        }
        return true;
    }
}
