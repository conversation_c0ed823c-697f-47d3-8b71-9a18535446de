package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import java.time.Instant;
import java.util.List;

import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class WhatsAppMessageDomain implements MessageDomain {

    MessageDomainId messageId;
    Instant time;
    WhatsAppAliasDomain sender;
    WhatsAppAliasDomain recipient;
    String content;
    byte[] file;
    List<FeelingDomain> feelingList;
    List<TagDomain> tagList;


    @Override
    public MessageDomainId messageId() {
        return messageId;
    }

    @Override
    public Instant time() {
        return time;
    }

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipient.getValue();
    }

    @Override
    public String content() {
        return content;
    }

    @Override
    public List<FeelingDomain> feelingList() {
        return feelingList;
    }

    @Override
    public List<TagDomain> tagList() {
        return tagList;
    }
}
