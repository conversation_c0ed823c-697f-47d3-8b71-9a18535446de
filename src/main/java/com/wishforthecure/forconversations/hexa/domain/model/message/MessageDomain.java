package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.ValueObject;

@Entity
@ValueObject
public interface MessageDomain {
    MessageDomainId messageId();

    Instant time();

    String getSender();

    String getRecipients();

    String content();

    List<FeelingDomain> feelingList();

    List<TagDomain> tagList();
}
