package com.wishforthecure.forconversations.hexa.domain.port;

import com.wishforthecure.forconversations.hexa.application.dto.CreateConversationCommand;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import reactor.core.publisher.Mono;

public interface CreateConversationUseCase {
    Mono<ConversationDomainId> create(CreateConversationCommand command);
}
