package com.wishforthecure.forconversations.hexa.domain.model.filter;

import java.util.Set;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;

import lombok.Value;

@ValueObject
@Value
public class KeywordFilter implements MessageFilter {

    Set<String> keywords;
    FilterMode mode;

    @Override
    public boolean apply(MessageDomain message) {
        String content = message.content();
        boolean matches = keywords.stream().anyMatch(kw -> content.contains(kw));
        return mode == FilterMode.INCLUDE ? matches : !matches;
    }

    public static MessageFilter of(Set<String> of, FilterMode exclude) {
        return new KeywordFilter(of, exclude);
    }
}
