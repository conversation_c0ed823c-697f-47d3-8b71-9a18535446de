package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailSourceDomain implements SourceDomain {

    SourceDomainId sourceId;
    Instant time;
    List<EmailMessageDomain> messages;
    byte[] source;
    SourceDomainType sourceType = SourceDomainType.EMAIL;

    public List<EmailMessageDomain> getEmailMessageDomainList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'getEmailMessageDomainList'");
    }
}
