package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainId;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;

@Repository
public interface SourceRepository {
    SourceDomain save(SourceDomain source);

    Optional<SourceDomain> findById(SourceDomainId id);
}
