package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailAliasDomain implements AliasDomain {

    private final AliasDomainId id;
    private final String name;
    private final String mail;

    @Override
    public AliasDomainId getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getValue() {
        return mail;
    }

    public static EmailAliasDomain create(AliasDomainId id, String name, String mail) {
        return new EmailAliasDomain(id, name, mail);
    }
}
