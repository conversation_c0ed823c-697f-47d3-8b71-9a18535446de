package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import lombok.Data;
import org.jmolecules.ddd.annotation.Entity;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.util.List;

@Entity
@Document(collection = "sources")
@Data
public abstract class AbstractSourceDomain implements SourceDomain {

    @Id
    protected SourceDomainId sourceId;

    protected Instant time;

    protected byte[] source;

    public abstract List<? extends MessageDomain> getMessages();
}
