package com.wishforthecure.forconversations.hexa.domain.model.source;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.Entity;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;

import lombok.Data;

@Entity
@Document(collection = "sources")
@Data
public abstract class AbstractSourceDomain implements SourceDomain {

    @Id
    protected SourceDomainId sourceId;

    protected Instant time;

    protected List<MessageDomain> messageDomainList;

    protected byte[] source;

    @Override
    public SourceDomainId getSourceId() {
        return sourceId;
    }

    @Override
    public Instant getTime() {
        return time;
    }

    @Override
    public List<MessageDomain> getMessages() {
        return messageDomainList;
    }

    @Override
    public byte[] getSource() {
        return source;
    }
}
