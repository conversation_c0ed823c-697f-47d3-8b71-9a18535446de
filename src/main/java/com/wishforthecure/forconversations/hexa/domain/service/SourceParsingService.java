package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainType;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class SourceParsingService {}
