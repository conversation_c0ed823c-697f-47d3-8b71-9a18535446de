package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import org.jmolecules.ddd.annotation.ValueObject;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.Value;

@ValueObject
@Value
public class EmailMessageDomain implements MessageDomain {

    MessageDomainId messageId;
    Instant time;
    EmailAliasDomain sender;
    List<EmailAliasDomain> recipients;
    String content;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;
    SourceDomainType sourceType;

    @Override
    public MessageDomainId messageId() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'messageId'");
    }

    @Override
    public Instant time() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'time'");
    }

    @Override
    public String content() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'content'");
    }

    @Override
    public List<FeelingDomain> feelingList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'feelingList'");
    }

    @Override
    public List<TagDomain> tagList() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'tagList'");
    }

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipients.stream().map(EmailAliasDomain::getValue).collect(Collectors.joining(","));
    }

    public static EmailMessageDomain create(MessageDomainId messageId, Instant time, EmailAliasDomain sender,
            List<EmailAliasDomain> recipients, String content, List<FeelingDomain> feelingDomainList,
            List<TagDomain> tagDomainList, SourceDomainType sourceType) {
        return new EmailMessageDomain(messageId, time, sender, recipients, content, feelingDomainList, tagDomainList,
                sourceType);
    }

}
