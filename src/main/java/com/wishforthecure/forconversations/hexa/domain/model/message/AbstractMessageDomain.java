package com.wishforthecure.forconversations.hexa.domain.model.message;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.Entity;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import lombok.Data;

@Entity
@Document(collection = "messages")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "messages")
@Data
public abstract class AbstractMessageDomain implements MessageDomain {

    @Id
    protected MessageDomainId messageId;
    protected Instant time;
    protected List<FeelingDomain> feelingList;
    protected List<TagDomain> tagList;
    protected SourceDomainType sourceType;
    protected String content;
    protected String sender;
    protected String recipients;

}
