package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.time.Instant;
import java.util.List;

/**
 * Domain interface for source entities.
 * This interface can be implemented by both Value Objects and Entities.
 */
public interface SourceDomain {
    SourceDomainId getSourceId();

    Instant getTime();

    List<MessageDomain> getMessages();

    byte[] getSource();
}
