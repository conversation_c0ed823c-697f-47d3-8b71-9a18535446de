package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.MessageDomain;
import java.time.Instant;
import java.util.List;
import org.jmolecules.ddd.annotation.Entity;

@Entity
public interface SourceDomain {
    SourceDomainId getSourceId();

    Instant getTime();

    List<? extends MessageDomain> getMessages();

    byte[] getSource();
}
