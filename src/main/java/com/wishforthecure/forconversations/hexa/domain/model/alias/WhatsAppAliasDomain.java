package com.wishforthecure.forconversations.hexa.domain.model.alias;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.AllArgsConstructor;
import lombok.Getter;

@ValueObject
@Getter
@AllArgsConstructor
public class WhatsAppAliasDomain implements AliasDomain {

    private final AliasDomainId id;
    private final String name;
    private final String mobile;

    @Override
    public AliasDomainId getId() {
        return id;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getValue() {
        return mobile;
    }

    public static WhatsAppAliasDomain create(AliasDomainId id, String name, String mobile) {
        return new WhatsAppAliasDomain(id, name, mobile);
    }
}
