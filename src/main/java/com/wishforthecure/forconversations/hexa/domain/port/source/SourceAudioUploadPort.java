package com.wishforthecure.forconversations.hexa.domain.port.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.WhatsAppMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.AbstractSourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jmolecules.ddd.annotation.AggregateRoot;
import org.springframework.data.annotation.TypeAlias;

import java.util.List;

public class SourceAudioUploadPort implements SourceUploadPort{

    private List<WhatsAppMessageDomain> messages;

    private SourceDomainType sourceType = SourceDomainType.WHATSAPP;

    @Override
    public List<WhatsAppMessageDomain> getMessages() {
        return messages;
    }
}
