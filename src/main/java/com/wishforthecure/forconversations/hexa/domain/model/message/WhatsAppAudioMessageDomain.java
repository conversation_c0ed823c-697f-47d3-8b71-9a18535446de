package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public record WhatsAppAudioMessageDomain(MessageDomainId messageId, Instant time, WhatsAppSenderAliasDomain sender,
                                         WhatsAppAliasDomain recipient, String content, byte[] file,
                                         List<FeelingDomain> feelingList,
                                         List<TagDomain> tagList) implements MessageDomain {

    public String getSender() {
        return sender.getValue();
    }

    public String getRecipients() {
        return recipient.getValue();
    }
}
