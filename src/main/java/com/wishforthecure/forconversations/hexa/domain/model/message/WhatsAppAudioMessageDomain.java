package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;

import java.time.Instant;
import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
public record WhatsAppAudioMessageDomain(
    MessageDomainId messageId,
    Instant time,
    WhatsAppAliasDomain sender,
    WhatsAppAliasDomain recipient,
    String content,
    byte[] file,
    List<FeelingDomain> feelingList,
    List<TagDomain> tagList
) implements MessageDomain {

    @Override
    public String getSender() {
        return sender.getValue();
    }

    @Override
    public String getRecipients() {
        return recipient.getValue();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        WhatsAppAudioMessageDomain that = (WhatsAppAudioMessageDomain) obj;
        return java.util.Objects.equals(messageId, that.messageId) &&
               java.util.Objects.equals(time, that.time) &&
               java.util.Objects.equals(sender, that.sender) &&
               java.util.Objects.equals(recipient, that.recipient) &&
               java.util.Objects.equals(content, that.content) &&
               java.util.Arrays.equals(file, that.file) &&
               java.util.Objects.equals(feelingList, that.feelingList) &&
               java.util.Objects.equals(tagList, that.tagList);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(messageId, time, sender, recipient, content,
                                     java.util.Arrays.hashCode(file), feelingList, tagList);
    }

    @Override
    public String toString() {
        return "WhatsAppAudioMessageDomain{" +
               "messageId=" + messageId +
               ", time=" + time +
               ", sender=" + sender +
               ", recipient=" + recipient +
               ", content='" + content + '\'' +
               ", file=" + java.util.Arrays.toString(file) +
               ", feelingList=" + feelingList +
               ", tagList=" + tagList +
               '}';
    }
}
