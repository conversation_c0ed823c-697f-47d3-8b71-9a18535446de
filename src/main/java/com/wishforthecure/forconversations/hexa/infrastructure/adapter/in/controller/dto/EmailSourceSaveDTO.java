package com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EmailSourceSaveDTO {

    private String sourceId;
    private int messageCount;
    private List<EmailMessageDomain> emailMessageList;

    public EmailSourceSaveDTO(String sourceId, int messageCount) {
        this.sourceId = sourceId;
        this.messageCount = messageCount;
    }
}
